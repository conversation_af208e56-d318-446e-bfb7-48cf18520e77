import { DIALOG_DATA } from '@angular/cdk/dialog';
import { CommonModule } from '@angular/common';
import { Component, inject, OnInit, signal, viewChild } from '@angular/core';
import { IWorkItemInfo, JobCreate } from '@norms/ops/shared';
import { SystemDataFacade, WorkDataFacade } from '@norms/shared/core';
import { AppConfirmationService, AppDialog, AppNavigationService, injectDialogInstance, ViewSizeConstants } from '@tec/rad-core/abstractions';
import { SelectItem } from '@tec/rad-core/utils';
import { StatusColorPipe } from '@tec/rad-ui/common';
import { RadDialogLayout } from '@tec/rad-ui/layout';
import { RadExpressForm, RadFormConfig } from '@tec/rad-xui/form';
import { DxDataGridModule } from 'devextreme-angular';
import { ButtonModule } from 'primeng/button';
import { RadBadge } from '@tec/rad-ui/component';
import { CreateJobInput, WorkItemRef } from '@norms/shared/data-ops';
import { JobService } from '../../core/job-service';
import { JobDataService } from '../../core';







@AppDialog({
    width: ViewSizeConstants.Large ,

})
@Component({
    selector: 'ops-create-job-scope',
    templateUrl: './create-job-scope.view.html',
    styles: [],
    imports:[
    //Add required imports here
    CommonModule,
    RadDialogLayout,
    RadExpressForm,
    DxDataGridModule,
    ButtonModule,
    StatusColorPipe,
    RadBadge
]
})
export class CreateJobScopeView implements OnInit {

    #system = inject(SystemDataFacade);
    #work = inject(WorkDataFacade);
    #jobFacade = inject(JobDataService);
    #dialog = injectDialogInstance();
    #nav = inject(AppNavigationService);
    #confirm = inject(AppConfirmationService);
    form = viewChild(RadExpressForm);
    protected dialogData: JobCreate = inject<JobCreate>(DIALOG_DATA);
    protected loading = signal(false);
    model = new CreateJobInput();
    protected selectedDefects: string[] = [];
    defects = signal<IWorkItemInfo[]>([]);
    subsystems = SelectItem.mapAll(this.#system.subsystems);
    incidents = SelectItem.mapAll(this.#work.incidents);
    subsystemTypes = SelectItem.mapAll(this.#system.subsystemTypes);
    jobTypes = SelectItem.mapAll(this.#work.jobTypes);




    fields: RadFormConfig = [

        { key: 'subsystemId', label: 'Feeder', type: 'select', row: 1, values: this.subsystems},
        { key: 'incidentId', label: 'Incident', type: 'select', row: 1, values: this.incidents},
        { key: 'jobTypeId', label: 'Job Type', type: 'select', row: 2, values: this.jobTypes},
        { key: 'plannedStart', label: 'Start Date', type: 'datepicker', row: 3,
            props: { showTime: true, showClearButton: true, showTodayButton: true },
            hooks: {
                onInit: (field) => {
                    const form = field.formControl?.parent;
                    const endDateField = form?.get('plannedEnd');

                        field.formControl?.valueChanges.subscribe(start => {
                        if (!start || !endDateField) return;

                        const end = endDateField.value;
                        const startDate = new Date(start);
                        const minEndDate = new Date(startDate.getTime() + 60 * 60 * 1000); // +1 hour

                        if (!end || new Date(end) <= startDate) {
                            endDateField.setValue(minEndDate);
                        }
                    });
                }
            }


        },
        { key: 'plannedEnd', label: 'End Date', type: 'datepicker', row: 3, props: { showTime: true, showClearButton: true, showTodayButton: true } },
    ]


    async ngOnInit() {

        if(this.dialogData.items?.length > 0) {
            this.defects.set(this.dialogData.items);
            const defect = this.dialogData.items[0];
            //this.model.subsystemId = defect.subsystemId;
            //this.model.incidentId = defect.incidentId;
        }

        this.model.subsystemId = this.dialogData.subsystemId;
        this.model.incidentId = this.dialogData.incidentId;
        this.model
    }

    cancel() {
        this.#dialog.cancel();
    }


    async submit() {


        if(!this.form().validate()) {
            return;
        }


        this.loading.set(true);

            const items = this.dialogData.items.map(i => {
                return new WorkItemRef({
                    type:i.workItemType,
                    id:i.id,
                    });
            });

            this.model.items = items;
            this.model.isolationDeviceId = this.dialogData.deviceId;

            const result = await this.#jobFacade.createJob(this.model);

            this.loading.set(false);

            if(result.isSuccess) {
                const jobId = result.value;
                await this.#nav.goTo("JobPage", jobId)
            } else {
                this.#confirm.error("Error creating job", "Error");
            }

            this.#dialog.confirm(result.value);

    }


    protected async addDefect() {

        throw new Error("Method not implemented.");
    }



}
