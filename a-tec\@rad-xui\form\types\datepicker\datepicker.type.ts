import { Component, ChangeDetectionStrategy, Type } from '@angular/core';
import { FieldType, FieldTypeConfig, FormlyFieldConfig } from '@ngx-formly/core';
import { FormlyFieldProps } from '@ngx-formly/primeng/form-field';
import { RadFormFieldProps } from '../../form-types';

interface DatepickerProps extends RadFormFieldProps {
  defaultDate?: Date;
  dateFormat?: string;
  hourFormat?: string;
  showTime?: boolean;
  showSeconds?: boolean;
  stepMinute?: number;
  showIcon?: boolean;
  showClear?: boolean;
  showButtonBar?: boolean;
  showOtherMonths?: boolean;
  selectOtherMonths?: boolean;
  selectionMode?: string;
  numberOfMonths?: number;
  inline?: boolean;
  readonlyInput?: boolean;
  touchUI?: boolean;
  monthNavigator?: boolean;
  yearNavigator?: boolean;
  yearRange?: string;
  
  
}

export interface FormlyDatepickerFieldConfig extends FormlyFieldConfig<DatepickerProps> {
  type: 'datepicker' | Type<RadFormlyDatepicker>;
}

@Component({
    selector: 'rad-formly-datepicker',
    template: `
    <p-datepicker
      [defaultDate]="props.defaultDate"
      [dateFormat]="props.dateFormat"
      [hourFormat]="props.hourFormat || 12"
      [showTime]="props.showTime"
      [stepMinute]="props.stepMinute || 5"
      [showSeconds]="props.showSeconds"
      [showIcon]="props.showIcon"
      [showButtonBar]="props.showButtonBar"
      [showOtherMonths]="props.showOtherMonths"
      [selectOtherMonths]="props.selectOtherMonths"
      [selectionMode]="props.selectionMode || 'single'"
      [numberOfMonths]="props.numberOfMonths"
      [inline]="props.inline"
      [readonlyInput]="props.readonlyInput"
      [touchUI]="props.touchUI"
      [monthNavigator]="props.monthNavigator"
      [yearNavigator]="props.yearNavigator"
      [yearRange]="props.yearRange"
      [placeholder]="props.placeholder"
      [showClear]="props.showClear ?? false"
      [formControl]="formControl"
      [formlyAttributes]="field"

      appendTo="body"
      fluid
    >
    </p-datepicker>
  `,
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class RadFormlyDatepicker extends FieldType<FieldTypeConfig<DatepickerProps>> {
  override defaultOptions?: Partial<FieldTypeConfig<DatepickerProps>> = {
    props: {
      numberOfMonths: 1,
      showIcon: true,
    },
  };
}
