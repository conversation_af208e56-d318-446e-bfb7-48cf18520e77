import { ModuleInfo } from "@tec/rad-core/abstractions";


import { OpsDefectModule } from "@norms/ops/defects";
import { OpsJobModule } from "@norms/ops/job";
import { OpsExplorerModule } from "@norms/ops/net";
import { SystemDataFacade,  WorkDataFacade } from "@norms/shared/core";
import { OpsResourcesModule } from "@norms/ops/resources";
import { OpsInventoryModule } from "@norms/ops/inventory";
import { OpsDashboardModule } from "@norms/ops/dashboards";
import { OpsSystemModule } from "@norms/ops/system";
import { resolveSignalrHub } from "@tec/rad-infra/signalr";
import { OpsWorkModule } from "@norms/ops/work";

export const OpsModule : ModuleInfo = {
    name: 'OpsModule',
    path: "ops",
    parts:[
      

        OpsDefectModule,
        OpsWorkModule,
        OpsExplorerModule,
        OpsResourcesModule,
        OpsInventoryModule,
        OpsSystemModule,
        OpsDashboardModule,
        OpsJobModule
    ],
    rootResolve:{
        systemData: SystemDataFacade.resolver,
        workData: WorkDataFacade.resolver,
        signalr: resolveSignalrHub
      }
}